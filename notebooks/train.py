import mlflow
from pyspark.sql import SparkSession
from pyspark.ml.feature import VectorAssembler
from pyspark.ml.classification import LogisticRegression
from pyspark.ml.evaluation import BinaryClassificationEvaluator

# Initialize Spark with MLflow tracking
spark = SparkSession.builder \
    .appName("LogisticRegressionExample") \
    .config("spark.master", "spark://spark-master:7077") \
    .getOrCreate()

# Set MLflow tracking URI
mlflow.set_tracking_uri("http://mlflow-tracking:5000")

# Create or get experiment
experiment_name = "titanic-survival-prediction"
try:
    experiment_id = mlflow.create_experiment(experiment_name)
except mlflow.exceptions.MlflowException:
    experiment_id = mlflow.get_experiment_by_name(experiment_name).experiment_id

mlflow.set_experiment(experiment_name)

# Load data from mounted dataset directory
data = spark.read.csv("/Dataset/train_data.csv", header=True, inferSchema=True)

# Select only numeric columns for features (excluding target and non-numeric columns)
numeric_cols = ["Pclass", "Age", "SibSp", "Parch", "Fare"]
# Filter out rows with null values in key columns
data_clean = data.filter(data.Age.isNotNull() & data.Fare.isNotNull())

# Prepare features
assembler = VectorAssembler(inputCols=numeric_cols, outputCol="features")
train_data = assembler.transform(data_clean)

# Split data
train, test = train_data.randomSplit([0.7, 0.3])

with mlflow.start_run():
    # Train model
    lr = LogisticRegression(featuresCol="features", labelCol="Survived")
    model = lr.fit(train)

    # Evaluate
    predictions = model.transform(test)
    evaluator = BinaryClassificationEvaluator(labelCol="Survived")
    auc = evaluator.evaluate(predictions)

    # Log parameters and metrics
    mlflow.log_param("data_path", "/Dataset/train_data.csv")
    mlflow.log_param("features", str(numeric_cols))
    mlflow.log_param("model_type", "LogisticRegression")
    mlflow.log_metric("auc", auc)

    # Note: Spark model logging requires proper MLflow artifact store setup
    # For now, we'll just log the metrics and parameters

    print(f"Model trained with AUC: {auc}")
    print(f"MLflow run ID: {mlflow.active_run().info.run_id}")