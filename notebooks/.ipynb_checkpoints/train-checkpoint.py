import mlflow
import mlflow.spark
from pyspark.sql import SparkSession
from pyspark.ml.feature import VectorAssembler
from pyspark.ml.classification import LogisticRegression
from pyspark.ml.evaluation import BinaryClassificationEvaluator

# Initialize Spark with MLflow tracking
spark = SparkSession.builder \
    .appName("LogisticRegressionExample") \
    .config("spark.jars.packages", "org.mlflow.mlflow-spark") \
    .getOrCreate()

# Set MLflow tracking URI
mlflow.set_tracking_uri("http://mlflow-tracking:5000")

# Load data from mounted dataset directory
data = spark.read.csv("/Dataset/train_data.csv", header=True, inferSchema=True)

# Prepare features
feature_cols = [c for c in data.columns if c != "label"]
assembler = VectorAssembler(inputCols=feature_cols, outputCol="features")
train_data = assembler.transform(data)

# Split data
train, test = train_data.randomSplit([0.7, 0.3])

with mlflow.start_run():
    # Train model
    lr = LogisticRegression(featuresCol="features", labelCol="label")
    model = lr.fit(train)
    
    # Evaluate
    predictions = model.transform(test)
    evaluator = BinaryClassificationEvaluator(labelCol="label")
    auc = evaluator.evaluate(predictions)
    
    # Log parameters, metrics and model
    mlflow.log_param("data_path", "/Dataset/train_data.csv")
    mlflow.log_metric("auc", auc)
    mlflow.spark.log_model(model, "spark-logistic-regression")
    
    print(f"Model trained with AUC: {auc}")
    print(f"MLflow run ID: {mlflow.active_run().info.run_id}")