import seffaflik2 as sf
from datetime import datetime, timedelta
import pandas as pd
import mysql.connector
from sqlalchemy import create_engine
import sys
import time
import traceback
import os

print("Starting epias_demand.py script...")
print(f"Current working directory: {os.getcwd()}")
print(f"Files in current directory: {os.listdir('.')}")

# EPIAS API credentials
myMail = "<EMAIL>"
myPsw = "123456789qQ@"
yesterday_string = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

# Function to split date range into yearly chunks
def get_yearly_chunks(start_date, end_date):
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    chunks = []
    
    # Align to start of year if not already
    if start.month != 1 or start.day != 1:
        year_end = datetime(start.year, 12, 31)
        if year_end > end:
            # If end date is in the same year, just use the whole period
            chunks.append((start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')))
            return chunks
        
        # Add partial first year
        chunks.append((start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')))
        start = datetime(start.year + 1, 1, 1)
    
    # Process full years
    while start < end:
        year_end = datetime(start.year, 12, 31)
        if year_end > end:
            year_end = end
        
        chunks.append((start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')))
        
        # Move to next year
        start = datetime(start.year + 1, 1, 1)
    
    return chunks

try:
    print("Authenticating with EPIAS API...")
    myTGT = sf.epias_tgt(myMail, myPsw)
    print("Authentication successful")
    
    # Get date chunks
    date_chunks = get_yearly_chunks("2023-01-01", yesterday_string)
    print(f"Retrieving demand data in {len(date_chunks)} yearly chunks:")
    for i, (chunk_start, chunk_end) in enumerate(date_chunks):
        print(f"  Chunk {i+1}: {chunk_start} to {chunk_end}")
    
    # Initialize an empty DataFrame to store all results
    all_demand_data = pd.DataFrame()
    
    # Retrieve data in chunks
    for i, (chunk_start, chunk_end) in enumerate(date_chunks):
        print(f"\nProcessing chunk {i+1}/{len(date_chunks)}: {chunk_start} to {chunk_end}")
        try:
            chunk_demand = sf.epias_demand(chunk_start, chunk_end, myTGT, myMail, myPsw)
            print(f"Retrieved {len(chunk_demand)} demand records")
            
            # Append to the main DataFrame
            if all_demand_data.empty:
                all_demand_data = chunk_demand
            else:
                all_demand_data = pd.concat([all_demand_data, chunk_demand], ignore_index=True)
            
            # Add a small delay to avoid overwhelming the API
            if i < len(date_chunks) - 1:
                print("Waiting 3 seconds before next request...")
                time.sleep(3)
                
        except Exception as e:
            print(f"Error retrieving data for chunk {chunk_start} to {chunk_end}: {e}")
            print("Traceback:")
            traceback.print_exc()
            print("Continuing with next chunk...")
    
    # Remove any potential duplicates
    all_demand_data = all_demand_data.drop_duplicates()
    
    print(f"Retrieved a total of {len(all_demand_data)} demand records from {date_chunks[0][0]} to {date_chunks[-1][1]}")
    realized_demand = all_demand_data
    
    # Check if there are datetime columns with timezone information
    datetime_columns = realized_demand.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64']).columns
    
    # Convert timezone-aware datetime columns to timezone-naive
    for col in datetime_columns:
        if hasattr(realized_demand[col], 'dt') and realized_demand[col].dt.tz is not None:
            print(f"Converting timezone-aware column '{col}' to timezone-naive")
            realized_demand[col] = realized_demand[col].dt.tz_localize(None)
    
    # Create output directory if it doesn't exist
    output_dir = "../Dataset/weather"
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV in the weather dataset directory
    output_file = os.path.join(output_dir, "epias_demand_data.csv")
    realized_demand.to_csv(output_file, index=False)
    print(f"Saved demand data to {output_file}")

    # Also save backup locally for debugging
    realized_demand.to_excel("realized_demand.xlsx", index=False)
    print("Saved backup to realized_demand.xlsx")
    
    # Connect to MySQL and save the data
    try:
        print("\nConnecting to MySQL database...")
        # # Create SQLAlchemy engine for pandas to_sql
        # engine = create_engine('mysql+pymysql://root:rootpassword@mysql/mldata')
        
        # # Connect with mysql.connector for table creation and management
        # conn = mysql.connector.connect(
        #     host="mysql",
        #     user="root",
        #     password="rootpassword",
        #     database="mldata"
        # )
        # cursor = conn.cursor()
        
        # # Create realized_demand table
        # print("Creating realized_demand table...")
        
        # # Inspect the columns in the DataFrame to create appropriate table schema
        # print(f"DataFrame columns: {realized_demand.columns.tolist()}")
        
        # # Create a basic table structure - only with date_time, hour, and consumption
        # cursor.execute("""
        # CREATE TABLE IF NOT EXISTS realized_demand (
        #     date_time DATETIME NOT NULL PRIMARY KEY,
        #     hour INT,
        #     consumption FLOAT
        # ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        # """)
        
        # # Commit the schema changes
        # conn.commit()
        
        # # Truncate table to ensure clean data
        # print("Clearing existing data...")
        # cursor.execute("TRUNCATE TABLE realized_demand")
        # conn.commit()
        
        # # Prepare data for insertion
        # print("Preparing data for MySQL insertion...")
        # db_data = realized_demand.copy()
        
        # # Map the columns from the API response to our database schema
        # # Handle date column - find the date/time column in the DataFrame
        # date_col = None
        # for col in db_data.columns:
        #     if 'date' in col.lower() or 'time' in col.lower():
        #         date_col = col
        #         break
        
        # if date_col:
        #     db_data.rename(columns={date_col: 'date_time'}, inplace=True)
        # else:
        #     print("WARNING: No date/time column found in the data")
            
        # # Extract hour from date_time if it exists
        # if 'date_time' in db_data.columns and pd.api.types.is_datetime64_any_dtype(db_data['date_time']):
        #     db_data['hour'] = db_data['date_time'].dt.hour
        
        # # Map consumption column - adjust based on actual column names
        # consumption_col = None
        # for col in db_data.columns:
        #     if col.lower() == 'consumption' or (col.lower().startswith('consumption') and 'forecast' not in col.lower()):
        #         consumption_col = col
        #         print(f"Found consumption column: {col}")
        #         break
                
        # if consumption_col:
        #     db_data.rename(columns={consumption_col: 'consumption'}, inplace=True)
        # else:
        #     print("WARNING: No consumption column found in the data")
        #     print(f"Available columns: {db_data.columns.tolist()}")
        #     sys.exit(1)
        
        # # Explicitly drop any columns that might contain forecast data
        # for col in db_data.columns:
        #     if 'forecast' in col.lower():
        #         print(f"Dropping forecast column: {col}")
        #         db_data = db_data.drop(columns=[col])
        
        # # Select only required columns: date_time, hour, consumption
        # required_columns = ['date_time']
        # if 'hour' in db_data.columns:
        #     required_columns.append('hour')
        # if 'consumption' in db_data.columns:
        #     required_columns.append('consumption')
        # else:
        #     print("ERROR: 'consumption' column not found after renaming")
        #     sys.exit(1)
            
        # # Ensure we only keep the columns we need
        # db_data = db_data[required_columns]
        
        # # Print the final columns to verify
        # print(f"Final columns for database insertion: {db_data.columns.tolist()}")
        
        # # Insert data using pandas to_sql
        # print("Saving demand data to MySQL...")
        # db_data.to_sql('realized_demand', con=engine, if_exists='append', index=False, chunksize=1000)
        
        # # Create index for better query performance
        # print("Creating indexes...")
        # try:
        #     cursor.execute("CREATE INDEX idx_realized_demand_date_time ON realized_demand(date_time)")
        #     print("Created index on date_time column")
        # except mysql.connector.Error as err:
        #     if err.errno == 1061:  # Error code for duplicate key
        #         print("Index already exists on date_time column")
        #     else:
        #         raise
        
        # # Verify data was inserted correctly
        # cursor.execute("SELECT COUNT(*) FROM realized_demand")
        # demand_count = cursor.fetchone()[0]
        
        # print(f"\nVerification: realized_demand has {demand_count} rows (expected: {len(db_data)})")
        
        # print("\nData saved successfully to MySQL database")
    except Exception as e:
        print(f"Error saving data to MySQL: {e}")
        print("Traceback:")
        traceback.print_exc()
        sys.exit(1)
    finally:
        # Close the cursor and connection
        # if 'conn' in locals() and conn.is_connected():
        #     cursor.close()
        #     conn.close()
            print("MySQL connection closed")
    
    print("\nAll realized demand data has been saved to CSV file")
    print("The output contains:")
    print(f"- epias_demand_data.csv with {len(realized_demand)} rows in ../Dataset/weather/ directory")
    
    # Success
    sys.exit(0)
    
except Exception as e:
    print(f"Unhandled error: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)
